cmake_minimum_required(VERSION 3.10)

project(webots_simulation)

find_package(ament_cmake REQUIRED)

set(INSTALL_CONTROLLER_TO_SOURCE_DIR TRUE) # 是否将执行文件拷贝到源码目录

if(DEFINED ENV{WEBOTS_HOME})
  set(WEBOTS_HOME $ENV{WEBOTS_HOME})
else()
  set(WEBOTS_HOME /usr/local/webots)
endif()

if(EXISTS ${WEBOTS_HOME} AND NOT CROSS_COMPILE)
  message("Build Webots Simulation. WEBOTS_HOME: ${WEBOTS_HOME}")
  link_directories(${WEBOTS_HOME}/lib/controller)
  set(WEBOTS_LIBRARIES libController.so libCppController.so libcar.so
                       libCppCar.so libCppDriver.so)

  set(WEBOTS_INCLUDES ${WEBOTS_HOME}/include/controller/c
                      ${WEBOTS_HOME}/include/controller/cpp)

  add_subdirectory(controllers/webots_chitu)
  add_subdirectory(controllers/object_process)

  install(DIRECTORY worlds DESTINATION projects)
  install(DIRECTORY protos DESTINATION projects)

endif()

ament_package()
