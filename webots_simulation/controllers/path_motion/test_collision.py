#!/usr/bin/env python3
"""
测试碰撞检测功能的简单脚本
"""

import math

class MockSupervisor:
    """模拟Supervisor类用于测试"""
    def __init__(self):
        self.time = 0
        
    def getTime(self):
        return self.time
        
    def step(self, time_step):
        self.time += time_step / 1000.0
        return 0 if self.time < 10 else -1  # 模拟10秒后结束
        
    def getSelf(self):
        return MockNode()
        
    def getFromDef(self, name):
        if name == "WEBOTS_VEHICLE0":
            return MockNode([1.0, 1.0, 0.0])  # 固定位置
        return None

class MockNode:
    """模拟节点类"""
    def __init__(self, position=[0, 0, 0]):
        self.position = position
        
    def getField(self, field_name):
        return MockField(self.position)
        
    def setVelocity(self, velocity):
        pass

class MockField:
    """模拟字段类"""
    def __init__(self, position):
        self.position = position[:]
        
    def getSFVec3f(self):
        return self.position[:]
        
    def setSFVec3f(self, position):
        self.position = position[:]
        
    def setSFRotation(self, rotation):
        pass

def test_collision_detection():
    """测试碰撞检测功能"""
    print("开始测试碰撞检测功能...")
    
    # 模拟两个位置
    pos1 = [0.0, 0.0, 0.0]
    pos2 = [0.3, 0.0, 0.0]  # 距离0.3米
    pos3 = [1.0, 1.0, 0.0]  # 距离较远
    
    threshold = 0.5
    
    # 测试距离计算
    def calculate_distance(p1, p2):
        return math.sqrt(
            (p1[0] - p2[0]) ** 2 +
            (p1[1] - p2[1]) ** 2 +
            (p1[2] - p2[2]) ** 2
        )
    
    dist1 = calculate_distance(pos1, pos2)
    dist2 = calculate_distance(pos1, pos3)
    
    print(f"位置1: {pos1}")
    print(f"位置2: {pos2}")
    print(f"位置3: {pos3}")
    print(f"阈值: {threshold}")
    print(f"距离1-2: {dist1:.3f}m - {'碰撞' if dist1 < threshold else '无碰撞'}")
    print(f"距离1-3: {dist2:.3f}m - {'碰撞' if dist2 < threshold else '无碰撞'}")
    
    print("碰撞检测测试完成!")

if __name__ == "__main__":
    test_collision_detection()
