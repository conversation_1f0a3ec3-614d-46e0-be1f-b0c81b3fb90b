#!/usr/bin/env python3
"""
测试时间暂停功能的简单脚本
"""

def test_time_pause_logic():
    """测试时间暂停和恢复的逻辑"""
    print("测试时间暂停逻辑...")
    
    # 模拟时间状态
    simulation_start_time = 0.0
    current_simulation_time = simulation_start_time
    
    # 模拟运行一段时间
    print("\n=== 正常运行阶段 ===")
    for i in range(5):
        current_simulation_time += 1.0  # 每次增加1秒
        elapsed = current_simulation_time - simulation_start_time
        print(f"仿真时间: {current_simulation_time:.1f}s, Elapsed: {elapsed:.1f}s")
    
    # 模拟碰撞检测到，暂停
    print("\n=== 检测到碰撞，暂停 ===")
    paused_elapsed_time = current_simulation_time - simulation_start_time
    pause_start_time = current_simulation_time
    is_stopped = True
    print(f"暂停时的elapsed time: {paused_elapsed_time:.1f}s")
    print(f"暂停开始时间: {pause_start_time:.1f}s")
    
    # 模拟暂停期间仿真继续运行
    print("\n=== 暂停期间（仿真继续，但elapsed time不更新）===")
    for i in range(3):
        current_simulation_time += 1.0
        print(f"仿真时间: {current_simulation_time:.1f}s, Elapsed: {paused_elapsed_time:.1f}s (暂停)")
    
    # 模拟恢复运动
    print("\n=== 恢复运动 ===")
    # 调整基准时间，使elapsed time从暂停时的值继续
    new_simulation_start_time = current_simulation_time - paused_elapsed_time
    is_stopped = False
    print(f"新的基准时间: {new_simulation_start_time:.1f}s")
    print(f"当前仿真时间: {current_simulation_time:.1f}s")
    print(f"恢复后的elapsed time: {current_simulation_time - new_simulation_start_time:.1f}s")
    
    # 模拟恢复后继续运行
    print("\n=== 恢复后继续运行 ===")
    simulation_start_time = new_simulation_start_time
    for i in range(3):
        current_simulation_time += 1.0
        elapsed = current_simulation_time - simulation_start_time
        print(f"仿真时间: {current_simulation_time:.1f}s, Elapsed: {elapsed:.1f}s")
    
    print("\n测试完成！")
    print("可以看到elapsed time在暂停期间保持不变，恢复后从暂停时的值继续增长。")

if __name__ == "__main__":
    test_time_pause_logic()
