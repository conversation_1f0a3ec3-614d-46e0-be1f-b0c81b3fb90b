#pragma once

#include <custom_msgs/msg/PerceptionObjects.h>
#include <webots_objects_msgs/msg/WbObjectList.h>
#include <Eigen/Geometry>
#include <Eigen/Core>
#include <shared_mutex>
#include "webots/Supervisor.hpp"

struct WbBoundingObject {
    std::string type;             // Box or Cylinder
    Eigen::Vector3f size;         // Box
    double height, radius;        // Cylinder
    Eigen::Isometry3f transform;  // 变换矩阵
    // Eigen::Vector3f translation;
    // Eigen::Quaternionf rotation;
};
struct BoundingBox3d {
    Eigen::Vector3f size;
    Eigen::Vector3f translation;
    double yaw;
};

struct NodeData {
    webots::Node *node_ptr;
    std::string def_name;                                      // webots DEF名称
    std::string prefix;                                        // webots DEF名称前缀
    int def_name_id;                                           // webots DEF名称id
    int webots_id;                                             // webots 内部id
    int output_id;                                             // 输出显示id
    custom_msgs::msg::PerceptionObject perception_object_msg;  // 给算法输出
    webots_objects_msgs::msg::WbObject webots_object_msg;      // 给rviz输出
};

const std::unordered_map<std::string, int> PrefixIdMap = {
    {"BOX", 0},
    {"DBOX", 10000},
    {"SUMO_VEHICLE", 20000},
};

class ObjectProcess
{
public:
    ObjectProcess(webots::Supervisor *supervisor, webots::Node *main_robot_node);
    ~ObjectProcess();

    void deleteAllObjects();

    void setRobotPose(double pos_x, double pos_y, double pos_theta);
    geometry_msgs::msg::Pose getRobotPose();

    void setObstacle(const webots_objects_msgs::msg::WbObject &webots_object_msg, bool in_base_link = false);
    void getObstacle(std::vector<std::string> prefix_list, webots_objects_msgs::msg::WbObjectList &webots_objects);
    void getObstacle(int type, webots_objects_msgs::msg::WbObjectList &webots_objects);

    void getPerceptionObjects(custom_msgs::msg::PerceptionObjects &perception_objects,
                              double pos_x_min = std::numeric_limits<double>::lowest(),
                              double pos_x_max = std::numeric_limits<double>::max(),
                              double pos_y_min = std::numeric_limits<double>::lowest(),
                              double pos_y_max = std::numeric_limits<double>::max());

    void updateUnknownNode();
    void updateNode(std::string prefix, int id, const webots_objects_msgs::msg::WbObject &webots_object_msg);

    bool updateAllObjectsPose();
    bool updateObjectPose(NodeData *node_data);

    void printNodeList();
    void printNode(const NodeData &node_data);

private:
    const webots::Supervisor *super_robot_;
    const webots::Node *main_robot_node_;

    std::shared_mutex node_list_mutex_;
    std::unordered_map<std::string, NodeData> node_list_;

    void addBoxObstacleToWb(std::string name, double size_x, double size_y, double size_z, double pos_x, double pos_y,
                            double pos_theta, double velocity);
    void updateBoxObstacleToWb(std::string name, double size_x, double size_y, double size_z, double pos_x,
                               double pos_y, double pos_theta, double velocity);

    void addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                               double pos_y, std::string type, double velocity, double radius);
    void updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                  double pos_y, std::string type, double velocity, double radius);

    void addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x, double pos_y,
                               std::string type, double velocity, const std::vector<geometry_msgs::msg::Point32> &points);

    void updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                  double pos_y, std::string type, double velocity,
                                  const std::vector<geometry_msgs::msg::Point32> &points);

    void findBoxes(const webots::Node *node, std::vector<const webots::Node *> &boxes);
    void findBoundingObject(const webots::Node *node, std::vector<const webots::Node *> &output);
    BoundingBox3d extractBoundingBox(std::vector<WbBoundingObject> &objects);

    int findMaxId(std::string prefix);
    std::vector<std::pair<std::string, std::string>> extractDefNameList();
};