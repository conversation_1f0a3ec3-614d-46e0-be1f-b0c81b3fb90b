#include "object_module.h"

#include "opencv2/opencv.hpp"
#include "pcl_conversions/pcl_conversions.h"
#include "uparam/uparam.h"

namespace uslam {
namespace module {
using namespace webots;
ObjectModule::ObjectModule()
{
    FLAGS_alsologtostderr = false;
    FLAGS_logtostderr = true;
    Ulog::getInstance().init("WebotsModule");

    unav::Time::init(true);

    uslam::transport::Transport::Instance()->init(uslam::transport::DDS, "WebotsModule");

    init();

    skip_update_.store(false);
    update_cnt_.store(0);
}

ObjectModule::~ObjectModule() {}

bool ObjectModule::init()
{
    super_robot_ = new webots::Supervisor();
    object_process_ = std::make_unique<ObjectProcess>(super_robot_, super_robot_->getFromDef("WEBOTS_VEHICLE0"));

    object_writer_ = std::make_shared<uslam::transport::Writer<custom_msgs::msg::PerceptionObjects>>(
        "/uslam/perception/perception_objects");
    all_object_writer_ =
        std::make_shared<uslam::transport::Writer<custom_msgs::msg::PerceptionObjects>>("/all_webots_objects");

    set_object_reader_ = std::make_shared<uslam::transport::Reader<webots_objects_msgs::msg::WbObjectList>>(
        "set_objects_topic", std::bind(&ObjectModule::setObjectCallback, this, std::placeholders::_1));

    get_service_ =
        std::make_shared<uslam::RosService<webots_objects_msgs::srv::Get_Request, webots_objects_msgs::srv::Get_Response>>(
            "get_objects", std::bind(&ObjectModule::getServiceCallback, this, std::placeholders::_1, std::placeholders::_2));
    get_service_->Init();

    set_service_ =
        std::make_shared<uslam::RosService<webots_objects_msgs::srv::Set_Request, webots_objects_msgs::srv::Set_Response>>(
            "set_objects", std::bind(&ObjectModule::setServiceCallback, this, std::placeholders::_1, std::placeholders::_2));
    set_service_->Init();
    return true;
}

int ObjectModule::step()
{
    return super_robot_->step(super_robot_->getBasicTimeStep());
}

void ObjectModule::setClock()
{
    unav::Time time_now;
    time_now.fromSec(super_robot_->getTime());
    unav::Time::setNow(time_now);
}

void ObjectModule::update()
{
    setClock();

    if ((update_cnt_.load() > 100 || update_cnt_.load() == 0) && !skip_update_.load()) {
        object_process_->updateUnknownNode();
        update_cnt_.store(0);
    }
    update_cnt_++;

    object_process_->updateAllObjectsPose();
    publishObjects(false);
    // publishObjects(true);
}

void ObjectModule::publishObjects(bool publish_all)
{
    const double x_min_ = -30.0;
    const double x_max_ = 30.0;
    const double y_min_ = -30.0;
    const double y_max_ = 30.0;

    custom_msgs::msg::PerceptionObjects objects_msg;
    objects_msg.header.frame_id = "base_link";
    unav::Time current_time;
    current_time.fromSec(super_robot_->getTime());
    objects_msg.header.stamp = current_time;
    if (publish_all) {
        object_process_->getPerceptionObjects(objects_msg);
        all_object_writer_->Write(objects_msg);
    } else {
        object_process_->getPerceptionObjects(objects_msg, x_min_, x_max_, y_min_, y_max_);
        object_writer_->Write(objects_msg);
    }
}

void ObjectModule::setServiceCallback(const webots_objects_msgs::srv::Set_Request::ConstPtr &req,
                                      webots_objects_msgs::srv::Set_Response::Ptr &res)
{
    skip_update_.store(true);
    for (const auto &item : req->objects.objects) {
        ULOGI("DDSWbGt::setServiceCallback %d type:%d frame_id:%s", item.action, item.type,
              req->objects.header.frame_id.c_str());
        if (item.action == webots_objects_msgs::msg::WbObject_Constants::DELETEALL) {
            object_process_->deleteAllObjects();
        } else if (item.action == webots_objects_msgs::msg::WbObject_Constants::ADD) {
            if (req->objects.header.frame_id == "base_link") {
                object_process_->setObstacle(item, true);
            } else {
                object_process_->setObstacle(item);
            }
        }
    }
    skip_update_.store(false);
    update_cnt_.store(0);
}

void ObjectModule::setObjectCallback(const std::shared_ptr<webots_objects_msgs::msg::WbObjectList> &msg)
{
    skip_update_.store(true);
    for (const auto &item : msg->objects) {
        ULOGI("DDSWbGt::setObjectCallback %d type:%d frame_id:%s", item.action, item.type, msg->header.frame_id.c_str());
        if (item.action == webots_objects_msgs::msg::WbObject_Constants::DELETEALL) {
            object_process_->deleteAllObjects();
        } else if (item.action == webots_objects_msgs::msg::WbObject_Constants::ADD) {
            if (msg->header.frame_id == "base_link") {
                object_process_->setObstacle(item, true);
            } else {
                object_process_->setObstacle(item);
            }
        }
    }
    skip_update_.store(false);
    update_cnt_.store(0);
}

void ObjectModule::getServiceCallback(const webots_objects_msgs::srv::Get_Request::ConstPtr &req,
                                      webots_objects_msgs::srv::Get_Response::Ptr &res)
{
    skip_update_.store(true);
    ULOGI("DDSWbGt::getServiceCallback type:%d", req->type);
    res->objects.objects.clear();
    if (req->type == webots_objects_msgs::srv::Get_Request_Constants::ALL) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::STATIC, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::STATIC) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::STATIC, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_CIRCLE) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_POLYGON) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_LINES) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES, res->objects);
    }

    res->success = true;
    skip_update_.store(false);
    update_cnt_.store(0);
}

}  // namespace module
}  // namespace uslam
