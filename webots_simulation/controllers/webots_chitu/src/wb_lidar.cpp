#include "wb_lidar.hpp"

#include "pcl/point_types.h"
#include "pcl_conversions/pcl_conversions.h"

struct PointXYZIRT {
    PCL_ADD_POINT4D
    PCL_ADD_INTENSITY;
    uint16_t ring;
    double time;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;
POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZIRT, (float, x, x)(float, y, y)(float, z, z)(float, intensity,
                                                                                       intensity)(uint16_t, ring,
                                                                                                  ring)(double, time, time))

DDSWbLidar::DDSWbLidar(webots::Robot *robot, int step, const UParam &param) : DDSWbSensor("lidar", robot, step, param)
{
}

bool DDSWbLidar::onInit()
{
    param_.param("wb_lidar_name", wb_lidar_name_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));

    lidar_ = (webots::Lidar *)robot_->getDevice(wb_lidar_name_);

    if (lidar_ == nullptr) return false;
    lidar_->enable(sensor_step_);
    lidar_->enablePointCloud();
    lidar_->setFrequency(10);

    pc_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::PointCloud2>>(topic_name_);

    printf("DDSWbLidar::init wb_lidar_name:%s step:%d topic_name:%s link_name:%s\n", wb_lidar_name_.c_str(),
           sensor_step_, topic_name_.c_str(), link_name_.c_str());
    return true;
}

void DDSWbLidar::onPublish()
{
    const webots::LidarPoint *points_ptr = lidar_->getPointCloud();

    pcl::PointCloud<PointXYZIRT>::Ptr point_cloud(new pcl::PointCloud<PointXYZIRT>);
    point_cloud->header.frame_id = link_name_;
    point_cloud->height = 1;
    // point_cloud->header.stamp = static_cast<uint64_t>(points_ptr->time * 1e6);
    int points_cnt = 0;
    double tmp_stamp = points_ptr->time;
    while (points_cnt < lidar_->getNumberOfPoints()) {
        PointXYZIRT point;
        if (std::isinf(points_ptr->x) || std::isinf(points_ptr->y) || std::isinf(points_ptr->z)) {
            points_ptr++;
            points_cnt++;
            continue;
        }

        point.x = points_ptr->x;
        point.y = points_ptr->y;
        point.z = points_ptr->z;
        point.intensity = 100.0;
        point.ring = points_ptr->layer_id;
        point.time = points_ptr->time;
        if (points_ptr->time - 0.0 < 0.0001) {
            points_ptr++;
            points_cnt++;
            continue;
        } else {
            if (points_ptr->time < tmp_stamp) {
                tmp_stamp = points_ptr->time;
            }
            point_cloud->points.push_back(point);
        }
        points_ptr++;
        points_cnt++;
    }

    // 需要用相对时间而不是绝对时间
    for (size_t i = 0; i < point_cloud->points.size(); ++i) {
        point_cloud->points[i].time -= tmp_stamp;
    }
    point_cloud->header.stamp = static_cast<uint64_t>(tmp_stamp * 1e6);
    point_cloud->width = point_cloud->points.size();

    sensor_msgs::msg::PointCloud2 pc_msg;
    pcl::toROSMsg(*point_cloud, pc_msg);
    pc_writer_->Write(pc_msg);
}
bool DDSWbLidar::isStepReady()
{
    if (!inited_) return false;

    const webots::LidarPoint *points_ptr = lidar_->getPointCloud();
    if (last_time_ == points_ptr->time) return false;
    last_time_ = points_ptr->time;
    return true;
}
